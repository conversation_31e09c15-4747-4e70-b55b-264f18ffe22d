@use 'colors';
@use 'variables';

//Mixins containing already existing classes mean that they modify a
//already existing component

/* Layout mixins */
@mixin flex-layout($flex-direction: row,
  $justify-content: center,
  $align-items: center,
  $gap: 0) {
  display: flex;
  justify-content: $justify-content;
  flex-direction: $flex-direction;
  align-items: $align-items;
  gap: $gap;
}

@mixin grid-layout($justify-content: center,
  $grid-template-columns: auto auto,
  $row-gap: 5px) {
  display: grid;
  justify-content: $justify-content;
  grid-template-columns: $grid-template-columns;
}

/* Mixins for browser specific styles */

@mixin control-buttons($left: 95%,
  $bottom: 30%,
  $background-color: colors.$yellow,
  $padding: 5px,
  $border-radius: variables.$rounded,
  $icon-filter: brightness(0) saturate(100%),
  $transform: rotate(0deg)) {
  :active {
    transform: scale(1.4);
  }

  transform: $transform;
  position: absolute;
  left: $left;
  bottom: $bottom;
  background-color: $background-color;
  padding: $padding;
  border-radius: variables.$rounded;

  &-icon {
    filter: $icon-filter;
  }
}

//Makes an element not selectable
@mixin no-user-select {
  -webkit-touch-callout: none !important;
  /* iOS Safari */
  -webkit-user-select: none !important;
  /* Safari */
  -khtml-user-select: none !important;
  /* Konqueror HTML */
  -moz-user-select: none !important;
  /* Old versions of Firefox */
  -ms-user-select: none !important;
  /* Internet Explorer/Edge */
  user-select: none !important;
}

//Adds a blur filter to an element
@mixin blur($blur-pixels: 5px) {
  -webkit-filter: blur($blur-pixels);
  -moz-filter: blur($blur-pixels);
  -o-filter: blur($blur-pixels);
  -ms-filter: blur($blur-pixels);
  filter: blur($blur-pixels);
  overflow: hidden;
}

//Makes the sliders on mobile to only react to dragging and not when you touch
//the actual slider the reason for this is when you scroll you can mess up the
//graph results by unwanted touching of the slider
@mixin non-clickable-slider {
  input[type='range'] {
    pointer-events: none;

    &::-moz-range-thumb {
      pointer-events: auto;
    }

    &::-webkit-slider-thumb {
      pointer-events: auto;
    }

    &::-ms-thumb {
      pointer-events: auto;
    }
  }
}

@mixin appearance($appearance: none) {
  -webkit-appearance: $appearance;
  -moz-appearance: $appearance;
  appearance: $appearance;
}

/*UI mixins */
@mixin pin-field($border: 1px solid colors.$gray-medium-lighter,
  $focus-border: 1px solid colors.$blue-light) {
  @include flex-layout;
  margin-right: 1rem;
  text-align: center;
  width: 40px;
  height: 50px;
  border-radius: variables.$rounded;
  border: $border;
  font-size: variables.$font-size-ml;

  &:focus {
    border: $focus-border;
    outline: none;
    box-shadow: none;
  }

  &:last-child {
    margin-right: 0 !important;
  }
}

@mixin scroll-bar-style($width: variables.$webkit-scroll-bar-width,
  $track-border-radius: 5px,
  $thumb-color: colors.$gray-mist,
  $handle-on-hover-color: colors.$gray-medium) {

  //Firefox specific
  @-moz-document url-prefix() {
    scrollbar-color: $thumb-color transparent;
  }

  ::-webkit-scrollbar {
    width: $width;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    border-radius: $track-border-radius;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $track-border-radius;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: $handle-on-hover-color;
  }
}

@mixin payout-info($width: 100%) {
  .payout-info {
    width: $width;
  }
}



@mixin carousel-indicator-item {
  background: colors.$white;
  border-radius: variables.$rounded-full;
  border: 2px solid colors.$blue-light;
  cursor: pointer;
  transition: ease 0.5s;
  width: 2px;
  height: 2px;
  padding: 2px;
  opacity: 0.5;
  margin: 0.1em;
}

@mixin modal-backdrop($z-index: 99999,
  $background: rgba($color: colors.$gray-dark, $alpha: 0.75)) {
  height: 100%;
  width: 100% !important;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: $z-index;
  //modal transparency
  background: $background !important;
}

@mixin multi-select-action {
  width: 20px;
  height: 20px;
  border: 1px solid colors.$white-faint;
  border-radius: variables.$rounded-full;
}

@mixin list-menu {
  @include flex-layout(column);
  background-color: colors.$blue-faint;
  position: relative;
}

@mixin secondary-list-menu {
  @include list-menu;
  background-color: transparent;

  .menu-card {
    &__title {
      @include font-style($font-size: variables.$font-size-s,
        $font-weight: variables.$font-normal );
      @include text-ellipsis;
      max-width: 18.75rem;
    }

    &__container {
      background-color: colors.$gray-faint;
    }

    &-item {
      &__main-icon {
        height: 25px;
      }
    }
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    .menu-card {
      &__container {
        background-color: colors.$white;
      }
    }
  }
}

@mixin clickable-underline-button {
  text-decoration: underline;
  color: colors.$blue-dark;
  cursor: pointer;
}

@mixin input-general($border-radius: variables.$rounded,
  $cursor: none,
  $height: 32px,
  $width: 100%,
  $padding: 23px 23px 23px 15px,
  $name: '_'

) {
  width: $width;
  height: $height;
  outline: none;
  padding: $padding;
  border-radius: $border-radius;

  // Style to be applied to a specific css class
  .#{$name} {
    max-width: $width;
    width: $width;
    height: $height;
    outline: none;
    padding: $padding;
    border-radius: $border-radius;
    cursor: $cursor;
  }
}

@mixin font-style($font-size: variables.$font-size-m,
  $font-weight: variables.$font-normal,
  $color: colors.$gray-dark,
  $line-height: normal,
  $text-transform: none) {
  font-size: calc($font-size + var(--font-modifier));
  font-weight: $font-weight;
  color: $color;
  line-height: $line-height;
  text-transform: $text-transform;
}

@mixin input-label {
  text-align: left;
  @include font-style($font-size: variables.$font-size-s);
}

@mixin dashboard-subtitle {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 0;
  @include font-style($font-weight: variables.$font-bold,
    $font-size: variables.$font-size-l );
}

@mixin text-ellipsis {
  overflow: clip;
  text-overflow: ellipsis;
}

@mixin button($bg-color: colors.$green,
  $font-color: colors.$white,
  $border: none,
  $height: 48px,
  $width: 100%,
  $border-radius: variables.$rounded,
  $font-size: variables.$font-size-ml,
  $cursor: pointer,
  $icon-gap: 15px,
  $justify-button-inner: center,
  $padding: 0.625rem,
  $char-limit: 100%) {
  @include flex-layout($justify-content: center);
  opacity: 1;
  padding: $padding;
  border: $border;
  background: $bg-color;
  border-radius: $border-radius;
  position: relative;
  cursor: $cursor;
  height: $height;
  width: $width;

  //Disabled button style
  &[disabled] {
    filter: grayscale(100%);
    pointer-events: none;
    cursor: not-allowed;
  }

  .button__inner {
    @include flex-layout($justify-content: $justify-button-inner,
      $gap: $icon-gap );
  }

  .button__text {
    max-width: $char-limit;
    white-space: nowrap;
    @include text-ellipsis;
    @include no-user-select;
    @include font-style($font-size: $font-size,
      $font-weight: variables.$font-semibold,
      $color: $font-color,
      $text-transform: uppercase);
    @include flex-layout($flex-direction: column,
      $align-items: center,
      $justify-content: center);
  }

  &:hover {
    opacity: 0.7;
    transition-duration: 0.5s;

    @media only screen and (max-width: variables.$mobile-devices) {
      // I hate safari so much
      opacity: 1 !important;
      transition-duration: 0 !important;
    }
  }

  &:after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: all 0.5s;
    border-radius: $border-radius;
    box-shadow: 0 0 7px 7px $bg-color;
  }

  &:active:after {
    box-shadow: 0 0 0 0 $bg-color;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 1;
    transition: 0s;
    border-radius: $border-radius;
  }
}

@mixin payout-progress-bar($value-bg: colors.$green,
  $bar-height: 5px,
  $bar-radius: variables.$rounded) {
  appearance: none;
  -webkit-appearance: none;
  border: none;
  outline: none;
  height: $bar-height;
  border-radius: $bar-radius !important;
  background-color: $value-bg;
}

@mixin progress-circles($circle-bg, $circle-border-bg, $size: 10px) {
  border-radius: variables.$rounded-full;
  width: $size;
  height: $size;
  background-color: $circle-bg;
  border: $circle-border-bg 1px solid;
}

@mixin first-letter-uppercase {
  text-transform: lowercase;

  &::first-letter {
    text-transform: uppercase;
  }
}

@mixin yellow-sun-bg-top-left($background: url('../../../assets/icon-onboarding_happy-sun-desktop.svg')) {
  background: $background no-repeat;
  background-position-y: -260px;
  background-position-x: -100px;
}

@mixin dashboard-balance($flex-direction: row,
  $justify-content: space-between,
  $gap: variables.$mytt-dashboard-element-gap) {
  .dashboard-balance {
    @include flex-layout($flex-direction, $justify-content);
    gap: $gap;
  }
}

@mixin hide-webkit-scrollbar($webkit-scrollbar-display: block,
  $firefox-scrollbar-width: none) {
  &::-webkit-scrollbar {
    display: $webkit-scrollbar-display;
  }

  @-moz-document url-prefix() {
    scrollbar-width: $firefox-scrollbar-width;
  }
}

@mixin disabled($opacity: 0.5) {
  cursor: not-allowed;
  pointer-events: none;
  opacity: $opacity;
  @include no-user-select;
}

@mixin transform($transform-argument: rotate(45deg)) {
  -webkit-transform: $transform-argument;
  /* WebKit */
  -moz-transform: $transform-argument;
  /* Mozilla */
  -o-transform: $transform-argument;
  /* Opera */
  -ms-transform: $transform-argument;
  /* Internet Explorer */
  transform: $transform-argument;
  /* CSS3 */
}

// Arrow rotation mixins for Safari compatibility
@mixin arrow-up {
  @include transform(rotate(-90deg));
}

@mixin arrow-down {
  @include transform(rotate(90deg));
}

@mixin arrow-left {
  @include transform(rotate(180deg));
}

@mixin arrow-right {
  // No rotation needed - this is the neutral position
}

@mixin circle-with-number($background-color: colors.$white,
  $color: colors.$green,
  $width: 30px,
  $height: 30px) {
  background-color: $background-color;
  border: 1px solid colors.$green;
  border-radius: variables.$rounded-full;
  height: $height;
  width: $width;
  @include flex-layout;
  @include font-style($color: $color,
    $font-size: variables.$font-size-xs,
    $line-height: none,
    $font-weight: variables.$font-bold );
}

@mixin payout-info-mobile-reset {

  //Fixes a lot of mobile issue with the payout info component
  .payout-info__label {
    margin-bottom: 0;
  }
}