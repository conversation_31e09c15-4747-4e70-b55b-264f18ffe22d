import { StrictMode, Suspense } from 'react'
import 'react-toastify/dist/ReactToastify.css'
import './common/style/main.scss'
// Localization
import './config/i18n'

import { BrowserRouter } from 'react-router'
import { ToastContainer } from 'react-toastify'
import InitializeUI from './common/components/InitializeUI'
import NavBar from './common/components/NavBar'
import SuspenseLoader from './common/components/SuspenseLoader'
import { useDeviceScreen } from './common/hooks/useDeviceScreen'
import { isLite } from './config/lite'
import AccessibilityProvider from './features/accessibility/AccessibilityProvider'
import AccessibilityControls from './features/accessibility/components/AccessibilityControls'
import { LegalServiceProvider } from './features/agreements/LegalServiceProvider'
import { AccountServiceProvider } from './features/authentication/AccountServiceProvider'
import SessionExtensionModal from './features/authentication/components/SessionExtensionModal'
import LiteAuthProvider from './features/authentication/LiteAuthProvider'
import MagicLogin from './features/authentication/pages/MagicLogin'
import { BankingServiceProvider } from './features/banking/BankingServiceProvider'
import FeedbackCard from './features/feedback/components/FeedbackCard'
import PageRoutes from './routes/PageRoutes'

/**
 * MyTontine app
 */
export default function App() {
  const { isMobileOrTablet } = useDeviceScreen()

  return (
    <StrictMode>
      <BrowserRouter>
        <AccessibilityProvider>
          <AccountServiceProvider>
            <BankingServiceProvider>
              <LegalServiceProvider>
                <LiteAuthProvider>
                  <Suspense fallback={<SuspenseLoader />}>
                    {!isLite && <MagicLogin />}
                    <InitializeUI>
                      {!isLite && <NavBar />}
                      {!isLite && <SessionExtensionModal />}

                      <PageRoutes />
                      <ToastContainer
                        position={isMobileOrTablet ? 'top-center' : 'top-right'}
                        autoClose={5_000}
                        theme="colored"
                      />
                      {!isLite && <FeedbackCard />}
                      {isLite && (
                        <AccessibilityControls
                          variant="gray-dark"
                          position="bottom-left"
                        />
                      )}
                    </InitializeUI>
                  </Suspense>
                </LiteAuthProvider>
              </LegalServiceProvider>
            </BankingServiceProvider>
          </AccountServiceProvider>
        </AccessibilityProvider>
      </BrowserRouter>
    </StrictMode>
  )
}
