import { useState } from 'react'
import { useSearchParams } from 'react-router'
import Layout from '../../../common/components/Layout'
import SentryErrorBoundary from '../../../common/components/SentryErrorBoundary'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { postMessageToParent } from '../../../common/hooks/useWindowPostMessage'
import {
  getDetectedIpCountry,
  parseParamsForEmail,
} from '../../../common/utils/UtilFunctions'
import SignInLiteUI from '../../authentication/components/SignInLiteUI'
import RegisterFormModal from '../../authentication/pages/RegisterFormModal'
import {
  chooseDefaultParams,
  useForecastParamsState,
} from '../hooks/usePreRegisterForecast'
import PublicTontinator from './PublicTontinator'

/**
 * Renders the tontinator with forecast params and controls which params should
 * the user pre-register with as part of the MyTontine lite sign up process,
 * that is used to hype up the product before launch
 */
const PreRegisterForecast = () => {
  const [urlSearchParams] = useSearchParams()
  const t = useTranslate()
  const { detectedCountry } = useLocalization()

  const [registerForm, setRegisterForm] = useState(
    urlSearchParams.has('register')
  )
  const [isOpenSignInModal, setIsOpenSignInModal] = useState(false)

  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: detectedCountry?.alpha3,
  })
  const { tontinatorParams } = supportedCountry

  const defaultParams = chooseDefaultParams({
    tontinatorParams,
    urlSearchParams,
    supportedCountry,
  })

  const {
    blueForecastParams,
    yellowForecastParams,
    incomeForecastParams,
    setBlueForecastParams,
    setYellowForecastParams,
    setIncomeForecastParams,
  } = useForecastParamsState({ defaultParams, tontinatorParams })

  if (registerForm) {
    return (
      <Layout
        containerWidth={'small'}
        containerHeight="auto"
        containerMt="nomt"
        layoutVariant="sun-blue-bg"
        hideDividerHeader
        hideMobileHeader
        headerTitle={t('ONBOARDING.SIGN_UP_MODAL_TITLE_GET_RESULTS')}
        headerVariant="spaced"
        headerTextColor="blue"
      >
        <RegisterFormModal
          hideSmallTitle
          backButtonAction={() => {
            setRegisterForm(false)
            postMessageToParent({
              eventId: 'SCROLL_TO_TOP_TON',
            })
          }}
          forecastPageRegisterModal
          forecastUserData={parseParamsForEmail({
            ...incomeForecastParams,
            countryOfResidence: getDetectedIpCountry(),
          })}
        />
      </Layout>
    )
  }

  return (
    <>
      <SignInLiteUI
        isOpenMobile={isOpenSignInModal}
        setIsOpenMobile={setIsOpenSignInModal}
        asModal
      />
      <Layout
        containerHeight="auto"
        containerMt="nomt"
        hideDividerHeader
        hideMobileHeader
      >
        <SentryErrorBoundary>
          <PublicTontinator
            incomeForecastParams={incomeForecastParams}
            setIncomeForecastParams={setIncomeForecastParams}
            setRegisterForm={setRegisterForm}
            blueForecastParams={blueForecastParams}
            setBlueForecastParams={setBlueForecastParams}
            yellowForecastParams={yellowForecastParams}
            setYellowForecastParams={setYellowForecastParams}
            isOpenSignInModal={isOpenSignInModal}
            setIsOpenSignInModal={setIsOpenSignInModal}
          />
        </SentryErrorBoundary>
      </Layout>
    </>
  )
}

export default PreRegisterForecast
