import { Routes } from 'react-router'
import { isLite } from '../config/lite'
import { buildPrivatePages, buildPublicPages } from './RouteBuilders'

const publicPages = buildPublicPages()
const privatePages = !isLite ? buildPrivatePages() : undefined

/**
 * Renders the generated page routes
 */
const PageRoutes = () => {
  return (
    <Routes>
      {publicPages}
      {privatePages}
    </Routes>
  )
}

export default PageRoutes
